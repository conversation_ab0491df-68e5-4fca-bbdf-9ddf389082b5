/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatmembermanagement;

import com.howbuy.crm.wechat.client.enums.CompanyNoEnum; // 临时保留用于业务逻辑转换
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatUserOuterService;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.ExternalUserInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 企业微信成员查询
 * <AUTHOR>
 * @date 2024/6/20 13:43
 * @since JDK 1.8
 */
@Service
public class WeChatMemberInfoService {

    @Autowired
    private WechatUserOuterService wechatUserOuterService;


    /**
     * 查询成员信息
     * @param companyNo  企微-企业主体
     * @param userId  企微-成员ID
     * @return
     */
    public ExternalUserInfoDTO queryWeChatMemberInfo(String companyNo, String userId) {
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        // 临时转换为CompanyNoEnum以保持业务逻辑不变
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(processedCompanyNo);
        return queryWeChatMemberInfo(companyNoEnum, userId);
    }

    /**
     * 查询成员信息
     * @param companyNoEnum  企微-企业主体
     * @param userId  企微-成员ID
     * @return
     * @deprecated 请使用 {@link #queryWeChatMemberInfo(String, String)} 方法
     */
    @Deprecated
    public ExternalUserInfoDTO queryWeChatMemberInfo(CompanyNoEnum companyNoEnum,String userId) {
        return wechatUserOuterService.queryUserByCompanyNo(companyNoEnum,userId);
    }

}

package com.howbuy.crm.wechat.service.repository;

import com.google.common.collect.Lists;
import com.howbuy.common.utils.Assert;
import com.howbuy.common.utils.StringUtil;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum; // 临时保留用于业务逻辑转换
import com.howbuy.crm.wechat.dao.bo.UserGroupInfoBO;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.dao.mapper.CmWechatCustInfoMapper;
import com.howbuy.crm.wechat.dao.mapper.customize.CustomizeCmWechatCustInfoMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import com.howbuy.crm.wechat.dao.po.CmWechatRelationPO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @description: 企业微信客户操作
 * @author: yu.zhang
 * @date: 2023/6/25 15:17 
 * @since JDK 1.8
 * @version: 1.0
 */
@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatCustInfoRepository {
    @Autowired
    private CmWechatCustInfoMapper cmWechatCustInfoMapper;
    @Autowired
    private CustomizeCmWechatCustInfoMapper customizeCmWechatCustInfoMapper;
    @Autowired
    private CommonRepository commonRepository;

    /**
     * @description:新增或修改企业微信客户信息
     * @param wechatCustInfo
     * @param companyNo 企微-企业主体
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/25 16:05
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertCmWechatCustInfo(CmWechatCustInfoPO wechatCustInfo, String companyNo){
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        // 临时转换为CompanyNoEnum以保持业务逻辑不变
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(processedCompanyNo);
        insertCmWechatCustInfo(wechatCustInfo, companyNoEnum);
    }

    /**
     * @description:新增或修改企业微信客户信息
     * @param wechatCustInfo
     * @param companyNoEnum 企微-企业主体
     * @return void
     * @author: yu.zhang
     * @date: 2023/6/25 16:05
     * @since JDK 1.8
     * @deprecated 请使用 {@link #insertCmWechatCustInfo(CmWechatCustInfoPO, String)} 方法
     */
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void insertCmWechatCustInfo(CmWechatCustInfoPO wechatCustInfo, CompanyNoEnum companyNoEnum){

        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");
        wechatCustInfo.setCompanyNo(companyNoEnum.getCode());
        List<CmWechatCustInfoPO> wechatCustList = customizeCmWechatCustInfoMapper.listWechatCustByExternalUserId(wechatCustInfo.getExternalUserId(),wechatCustInfo.getCompanyNo());

        if(CollectionUtils.isNotEmpty(wechatCustList)){
            wechatCustList.forEach(cmWechatCustInfo ->{
                cmWechatCustInfo.setHboneNo(wechatCustInfo.getHboneNo());
                cmWechatCustInfo.setUnionid(wechatCustInfo.getUnionid());
                cmWechatCustInfo.setNickName(wechatCustInfo.getNickName());
                cmWechatCustInfo.setWechatAvatar(wechatCustInfo.getWechatAvatar());
                cmWechatCustInfo.setHkHboneNo(wechatCustInfo.getHkHboneNo());
                cmWechatCustInfo.setModifier("sys");
                cmWechatCustInfo.setHkHboneNo(wechatCustInfo.getHkHboneNo());
                cmWechatCustInfo.setUpdateTime(new Date());
                cmWechatCustInfoMapper.updateByPrimaryKeySelective(cmWechatCustInfo);
            });
        }else{
            wechatCustInfo.setId(commonRepository.getWechatCustIdBySeq());
            wechatCustInfo.setCreator("sys");
            wechatCustInfo.setCreateTime(new Date());
            cmWechatCustInfoMapper.insertSelective(wechatCustInfo);
        }
    }

    /**
     * @description:根据企业IC和一账通查询客户信息
     * @param hboneNo
     * @param companyNo 企微-企业主体
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 16:05
     * @since JDK 1.8
     */
    public CmWechatCustInfoPO getExternalUserByHboneNo(String hboneNo, String companyNo){
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        // 临时转换为CompanyNoEnum以保持业务逻辑不变
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(processedCompanyNo);
        return getExternalUserByHboneNo(hboneNo, companyNoEnum);
    }

    /**
     * @description:根据企业IC和一账通查询客户信息
     * @param hboneNo
     * @param companyNoEnum 企微-企业主体
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 16:05
     * @since JDK 1.8
     * @deprecated 请使用 {@link #getExternalUserByHboneNo(String, String)} 方法
     */
    @Deprecated
    public CmWechatCustInfoPO getExternalUserByHboneNo(String hboneNo, CompanyNoEnum companyNoEnum){
        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");
        Assert.notNull(hboneNo, "一账通不能为空！");
        return customizeCmWechatCustInfoMapper.getExternalUserByHboneNo(companyNoEnum.getCode(),hboneNo);
    }

    /**
     * @description:根据企业IC和香港一账通查询客户信息
     * @param hkHoneNo
     * @param companyNo 企微-企业主体
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 16:05
     * @since JDK 1.8
     */
    public CmWechatCustInfoPO getExternalUserByHkHboneNo(String hkHoneNo, String companyNo) {
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        // 临时转换为CompanyNoEnum以保持业务逻辑不变
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(processedCompanyNo);
        return getExternalUserByHkHboneNo(hkHoneNo, companyNoEnum);
    }

    /**
     * @description:根据企业IC和香港一账通查询客户信息
     * @param hkHoneNo
     * @param companyNoEnum 企微-企业主体
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * @author: yu.zhang
     * @date: 2023/6/25 16:05
     * @since JDK 1.8
     * @deprecated 请使用 {@link #getExternalUserByHkHboneNo(String, String)} 方法
     */
    @Deprecated
    public CmWechatCustInfoPO getExternalUserByHkHboneNo(String hkHoneNo, CompanyNoEnum companyNoEnum) {
        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");
        Assert.notNull(hkHoneNo, "香港一账通不能为空！");
        return customizeCmWechatCustInfoMapper.getExternalUserByHkHboneNo(companyNoEnum.getCode(),hkHoneNo);
    }
    /**
     * @description:根据外部联系人id和企业Id查询微信客户信息
     * @param externalUserId
     * @param companyNo 企微-企业主体
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * <AUTHOR>
     * @date 2024/9/10 13:22
     * @since JDK 1.8
     */
    public CmWechatCustInfoPO getWechatCustByExternalUserId(String externalUserId, String companyNo){
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        // 临时转换为CompanyNoEnum以保持业务逻辑不变
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(processedCompanyNo);
        return getWechatCustByExternalUserId(externalUserId, companyNoEnum);
    }

    /**
     * @description:根据外部联系人id和企业Id查询微信客户信息
     * @param externalUserId
     * @param companyNoEnum 企微-企业主体
     * @return com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO
     * <AUTHOR>
     * @date 2024/9/10 13:22
     * @since JDK 1.8
     * @deprecated 请使用 {@link #getWechatCustByExternalUserId(String, String)} 方法
     */
    @Deprecated
    public CmWechatCustInfoPO getWechatCustByExternalUserId(String externalUserId, CompanyNoEnum companyNoEnum){
        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");
        Assert.notNull(externalUserId, "外部联系人Id不能为空！");
        return customizeCmWechatCustInfoMapper.getWechatCustByExternalUserId(externalUserId, companyNoEnum.getCode());
    }


    /**
     * @description 更新一帐通(账户中心mq接受时使用)
     * @param companyNo  企微-企业主体 不允许空
     * @param hbOneNo
     * @param unionId
     * @return
     * <AUTHOR>
     * @date 2024/6/19 3:19 PM
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateHbOneNoByUnionId(String companyNo,String hbOneNo, String unionId) {
        return customizeCmWechatCustInfoMapper.updateHbOneNoByUnionId(companyNo,unionId, hbOneNo);

    }
    /**
     * @description 更新香港一帐通(账户中心mq接受时使用)
     * @param companyNo  企微-企业主体 不允许空
     * @param hkHboneNo
     * @param unoinId
     * @return
     * <AUTHOR>
     * @date 2024/6/19 3:19 PM
     * @since JDK 1.8
     */
    public int updateHkHbOneNoByUnionId(String companyNo,String hkHboneNo, String unoinId) {
        return customizeCmWechatCustInfoMapper.updateHkHbOneNoByUnionId(companyNo,unoinId, hkHboneNo);
    }

    /**
     * @description 查询指定部门下的外部客户列表，如果客户没有加群，则只返回客户信息
     * @param companyNo  企微-企业主体 不允许空
     * @param externalUserIds 外部客户ID列表 不允许为空
     * @return List<UserGroupInfoBO>
     * <AUTHOR>
     * @date 2024/5/27 3:58 PM
     * @since JDK 1.8
     */
    public List<UserGroupInfoBO> selectUserGroupList(String companyNo,
                                              List<String> externalUserIds){
        if(CollectionUtils.isEmpty(externalUserIds) || StringUtil.isEmpty(companyNo)){
            return Lists.newArrayList();
        }
        return customizeCmWechatCustInfoMapper.selectUserGroupList(companyNo, externalUserIds);
    }

    /**
     * @description:(请在此添加描述)
     * @param companyNo  企微-企业主体 不允许空
     * @param hboneNo
     * @param consCodeList
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatRelationPO>
     * @author: xufanchao
     * @date: 2025/6/10 18:04
     * @since JDK 1.8
     */
    public  List<CmWechatRelationPO> queryWechatAddRelationList(String companyNo,
                                                                String hboneNo,
                                                                List<String> consCodeList) {
        return customizeCmWechatCustInfoMapper.queryWechatAddRelationList(companyNo,hboneNo, consCodeList);
    }
}

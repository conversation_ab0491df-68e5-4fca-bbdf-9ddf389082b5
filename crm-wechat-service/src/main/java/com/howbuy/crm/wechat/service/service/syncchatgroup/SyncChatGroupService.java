/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.syncchatgroup;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum; // 临时保留用于业务逻辑转换
import com.howbuy.crm.wechat.service.business.syncchatgroup.SyncChatGroupBusiness;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalGroupInfoDTO;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatExternalContactOuterService;
import com.howbuy.crm.wechat.service.repository.CmWechatEmpRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 同步群组信息接口实现类
 * <AUTHOR>
 * @date 2023/10/30 14:14
 * @since JDK 1.8
 */

@Slf4j
@Service
public class SyncChatGroupService {

    @Autowired
    private SyncChatGroupBusiness syncChatGroupBusiness;

    @Autowired
    private CmWechatEmpRepository cmWechatEmpRepository;

    @Autowired
    private WechatExternalContactOuterService wechatExternalContactOuterService;

    /**
     * @description: 同步企微客户群
     * @param companyNoList 企业编码列表
     * @author: jin.wang03
     * @date: 2023/10/30 18:39
     * @since JDK 1.8
     */
    public void syncChatGroupDataByCompanyNos(List<String> companyNoList) {
        // 调度业务逻辑处理
        log.info("[获取企业群ID]-任务开始！同步企业列表:{}",JSON.toJSONString(companyNoList));
        for (String companyNo : companyNoList) {
            String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
            try {
                List<String> empIdList = cmWechatEmpRepository.listWechatEmpId(processedCompanyNo);
                if (CollectionUtils.isEmpty(empIdList)) {
                    log.info("companyNo：{}，员工列表为空！无需同步企业群信息！", processedCompanyNo);
                    continue;
                }
                log.info("companyNo：{}，获取员工列表:{}", processedCompanyNo, empIdList);
                doSyncChatGroupData(processedCompanyNo, empIdList);
            } catch (Exception e) {
                log.error("companyNo：{}，[获取企业群ID]-任务异常",processedCompanyNo, e);
            }
        }

        log.info("[获取企业群ID]-任务结束！同步企业列表:{}",JSON.toJSONString(companyNoList));
    }

    /**
     * @description: 同步企微客户群
     * @param companyNoEnumList 同步参数
     * @author: jin.wang03
     * @date: 2023/10/30 18:39
     * @since JDK 1.8
     */
    public void syncChatGroupData(List<CompanyNoEnum> companyNoEnumList) {
        // 调度业务逻辑处理
        log.info("[获取企业群ID]-任务开始！同步企业列表:{}",JSON.toJSONString(companyNoEnumList));
        for (CompanyNoEnum companyNoEnum : companyNoEnumList) {
             String companyNo = companyNoEnum.getCode();
            try {
                List<String> empIdList = cmWechatEmpRepository.listWechatEmpId(companyNo);
                if (CollectionUtils.isEmpty(empIdList)) {
                    log.info("companyNo：{}，员工列表为空！无需同步企业群信息！", companyNoEnum);
                    continue;
                }
                log.info("companyNo：{}，获取员工列表:{}", companyNoEnum, empIdList);
                doSyncChatGroupData(companyNo, empIdList);
            } catch (Exception e) {
                log.error("companyNo：{}，[获取企业群ID]-任务异常",companyNoEnum, e);
            }
        }

        log.info("[获取企业群ID]-任务结束！同步企业列表:{}",JSON.toJSONString(companyNoEnumList));
    }



    /**
     * @description: 同步客户群信息
     * @param companyNo	企业编码
     * @param empIdList	员工userid集合
     * @author: jin.wang03
     * @date: 2023/10/31 13:59
     * @since JDK 1.8
     */
    private void doSyncChatGroupData(String companyNo, List<String> empIdList) {
        //!!!!!!!!员工表存了大量重复数据,没时间修,先去重
        empIdList = empIdList.stream().distinct().collect(Collectors.toList());


        // 每次从empIdList获取99个，放入企微接口的userid_list中，查询客户群列表
        int size = 99;
        int count = empIdList.size() / size;

        for (int i = 0; i <= count; i++) {
            List<String> subEmpIdList = empIdList.subList(i * size, Math.min((i + 1) * size, empIdList.size()));
            if (CollectionUtils.isEmpty(subEmpIdList)) {
                break;
            }
            // 临时转换为CompanyNoEnum以保持业务逻辑不变，后续WechatExternalContactOuterService也需要重构
            CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(companyNo);
            List<ExternalGroupInfoDTO> chatGroupList = wechatExternalContactOuterService.getChatGroupByUserIdList(subEmpIdList, companyNoEnum);
            log.info("companyNo:{}, 查询到的客户群个数:{}", companyNo,chatGroupList.size());
            if (CollectionUtils.isEmpty(chatGroupList)) {
                log.info("companyNo:{}, userId为：{} 的查询到的客户群列表为空",companyNo, subEmpIdList);
                continue;
            }

            // 将查询到的客户群列表，写入cm_wechat_group表中，无则新增，有则更新
            syncChatGroupBusiness.persistChatGroupList(companyNo, chatGroupList);
        }
    }
}
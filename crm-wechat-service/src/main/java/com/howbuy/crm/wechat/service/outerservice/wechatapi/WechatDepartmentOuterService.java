package com.howbuy.crm.wechat.service.outerservice.wechatapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum; // 临时保留用于业务逻辑转换
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.domain.externaluser.ExternalDeptDTOExternal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 企业内部开发- 服务端API- 部门管理- 成员管理 [/user/]
 * 【重要】从2022年8月15日10点开始，“企业管理后台 - 管理工具 - 通讯录同步”的新增IP将不能再调用此接口，企业可通过「获取部门ID列表」接口获取部门ID列表
 * https://developer.work.weixin.qq.com/document/path/90205
 * @author: yu.zhang
 * @date: 2023/6/19 10:50 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class WechatDepartmentOuterService extends AbsWechatOuterService{


    /**
     *通讯录管理-- 部门管理 获取部门列表
     */
    public static final String GET_DEPT_INFO_LIST = "department/list";

    /**
     * 获取部门列表
     * @param companyNo 企业编码
     * @return 部门列表
     */
    public List<ExternalDeptDTOExternal> queryDeptListByCompanyNo(String companyNo) {
        // 使用CompanyNoUtils处理企业编码
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        String responseStr = requestWithCompanyNo(GET_DEPT_INFO_LIST, processedCompanyNo, Maps.newHashMap(), Constants.METHOD_GET);
        return transferDeptDTO(responseStr);
    }

    /**
     * 获取部门列表
     * @param companyNoEnum  企微-企业主体
     * @return 部门列表
     * @deprecated 请使用 {@link #queryDeptListByCompanyNo(String)} 方法
     */
    @Deprecated
    public List<ExternalDeptDTOExternal> queryDeptListByCompanyNo(CompanyNoEnum companyNoEnum) {
        String companyNo = CompanyNoUtils.fromCompanyNoEnum(companyNoEnum);
        return queryDeptListByCompanyNo(companyNo);
    }


    /**
     * @description:(企微返回字段 转义)
     * @param responseStr
     * @return java.util.List<com.howbuy.crm.wechat.service.domain.externaluser.ExternalDeptDTO>
     * @author: haoran.zhang
     * @date: 2025/7/18 15:07
     * @since JDK 1.8
     */
    private  List<ExternalDeptDTOExternal> transferDeptDTO(String responseStr) {
//        {
//            "errcode": 0,
//                "errmsg": "ok",
//                "department": [
//            {
//                "id": 2,
//                    "name": "广州研发中心",
//                    "name_en": "RDGZ",
//                    "department_leader":["zhangsan","lisi"],
//                "parentid": 1,
//                    "order": 10
//            },
//            {
//                "id": 3,
//                    "name": "邮箱产品部",
//                    "name_en": "mail",
//                    "department_leader":["lisi","wangwu"],
//                "parentid": 2,
//                    "order": 40
//            }
//   ]
//        }
        List<ExternalDeptDTOExternal> deptList = Lists.newArrayList();
        JSONObject jsonObject = JSON.parseObject(responseStr);
        JSONArray deptJsonArray = jsonObject.getJSONArray("department");
        deptJsonArray.forEach(obj -> {
            JSONObject deptObj = (JSONObject) obj;
            ExternalDeptDTOExternal deptDTO = new ExternalDeptDTOExternal();
            deptDTO.setId((Integer) deptObj.get("id"));
            deptDTO.setName((String) deptObj.get("name"));
            deptDTO.setParentId((Integer) deptObj.get("parentid"));
            deptDTO.setOrder((Integer) deptObj.get("order"));

            deptDTO.setNameEn((String) deptObj.get("name_en"));

            List<String>  leaderList = Lists.newArrayList();
            JSONArray leaderArray = deptObj.getJSONArray("department_leader");
            if(leaderArray!=null &&   !leaderArray.isEmpty()){
                leaderArray.forEach(leaderObj -> leaderList.add((String) leaderObj));
            }
            deptDTO.setDepartmentLeader(leaderList);
            deptList.add(deptDTO);
        });
        return deptList;
    }

}

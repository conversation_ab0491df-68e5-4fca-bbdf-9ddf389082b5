/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.group;

import com.howbuy.crm.wechat.client.enums.CompanyNoEnum; // 临时保留用于业务逻辑转换
import com.howbuy.crm.wechat.service.business.group.WechatGroupBusiness;
import com.howbuy.crm.wechat.service.commom.utils.CompanyNoUtils;
import com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * @description: (企微群service)
 * <AUTHOR>
 * @date 2024-3-15 15:27:46
 * @since JDK 1.8
 */

@Slf4j
@Service
public class WechatGroupService {

    @Autowired
    private WechatGroupBusiness wechatGroupBusiness;

    /**
     * @description:(根据员工账号查询所有有他的群)
     * @param companyNo  企微-企业主体
     * @param externalUserId 外部客户ID
     * @return java.util.List<com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo>
     * @author: shuai.zhang
     * @date: 2024/3/15 16:25
     * @since JDK 1.8
     */
    public List<GroupChatInfo> getGroupInfoByUserId(String companyNo, String externalUserId) {
        String processedCompanyNo = CompanyNoUtils.getCompanyNo(companyNo);
        // 临时转换为CompanyNoEnum以保持业务逻辑不变
        CompanyNoEnum companyNoEnum = CompanyNoUtils.toCompanyNoEnum(processedCompanyNo);
        return getGroupInfoByUserId(companyNoEnum, externalUserId);
    }

    /**
     * @description:(根据员工账号查询所有有他的群)
     * @param companyNoEnum  企微-企业主体
     * @param externalUserId 外部客户ID
     * @return java.util.List<com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo>
     * @author: shuai.zhang
     * @date: 2024/3/15 16:25
     * @since JDK 1.8
     * @deprecated 请使用 {@link #getGroupInfoByUserId(String, String)} 方法
     */
    @Deprecated
    public List<GroupChatInfo> getGroupInfoByUserId(CompanyNoEnum companyNoEnum,String externalUserId) {
        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");
        return wechatGroupBusiness.getGroupInfoByUserId(companyNoEnum.getCode(),externalUserId);
    }

    /**
     * @description:(根据群ID获取群信息)
     * @param chatId
     * @param companyNo  企微-企业主体
     * @return com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo
     * @author: shuai.zhang
     * @date: 2024/4/2 13:46
     * @since JDK 1.8
     */
    public GroupChatInfo getGroupInfoByChatId(String chatId,String companyNo) {
        return wechatGroupBusiness.getGroupInfoByChatId(chatId,companyNo);
    }


    /**
     * @description:(根据群ID实时请求企微服务端获取群信息)
     * @param chatId 群Id
     * @param corpId 公司Id
     * @param corpSecret 公司密钥
     * @return com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo
     * @author: shuai.zhang
     * @date: 2024/4/2 13:46
     * @since JDK 1.8
     */
    public GroupChatInfo getDynamicGroupInfoByChatId(String chatId,String corpId, String corpSecret) {
        return wechatGroupBusiness.getDynamicGroupInfoByChatId(chatId,corpId, corpSecret);
    }
}